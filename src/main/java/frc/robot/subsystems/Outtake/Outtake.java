package frc.robot.subsystems.Outtake;

import com.ctre.phoenix6.configs.MotorOutputConfigs;
import com.ctre.phoenix6.hardware.TalonFX;
import com.ctre.phoenix6.signals.InvertedValue;
import com.ctre.phoenix6.signals.NeutralModeValue;
import com.google.errorprone.annotations.OverridingMethodsMustInvokeSuper;
import com.revrobotics.ColorSensorV3;
import com.revrobotics.spark.SparkBase.PersistMode;
import com.revrobotics.spark.SparkBase.ResetMode;
import com.revrobotics.spark.SparkFlex;
import com.revrobotics.spark.SparkLowLevel.MotorType;
import com.revrobotics.spark.config.SparkBaseConfig.IdleMode;
import com.revrobotics.spark.config.SparkFlexConfig;

import edu.wpi.first.wpilibj.DigitalInput;
import edu.wpi.first.wpilibj2.command.SubsystemBase;
import frc.robot.constants.IdConstants;

public class Outtake extends SubsystemBase {

    private TalonFX motor = new TalonFX(IdConstants.OUTTAKE_MOTOR);
    private double power;

    /** color sensor (measures coral based on proximity) - detects before rollers */
    private final ColorSensorV3 colorSensor = new ColorSensorV3(null); // change to proper channel
   

    public Outtake() {
        motor.getConfigurator().apply(new MotorOutputConfigs().withInverted(InvertedValue.CounterClockwise_Positive).withNeutralMode(NeutralModeValue.Brake));
    }

@OverridingMethodsMustInvokeSuper
    public void setPower(double power) {
        this.power = power;
        motor.setControl(new Voltage(power));
    }

}