package frc.robot.constants;

public class IdConstants {
    // Drivetrain
    public static final int DRIVE_FRONT_LEFT_ID = 1;
    public static final int STEER_FRONT_LEFT_ID = 2;
    public static final int ENCODER_FRONT_LEFT_ID = 3;
    public static final int DRIVE_FRONT_RIGHT_ID = 10;
    public static final int STEER_FRONT_RIGHT_ID = 11;
    public static final int ENCODER_FRONT_RIGHT_ID = 12;
    public static final int DRIVE_BACK_LEFT_ID = 7;
    public static final int STEER_BACK_LEFT_ID = 8;
    public static final int ENCODER_BACK_LEFT_ID = 9;
    public static final int DRIVE_BACK_RIGHT_ID = 4;
    public static final int STEER_BACK_RIGHT_ID = 5;
    public static final int ENCODER_BACK_RIGHT_ID = 6;
    public static final int PIGEON = 13;

    // LEDs
    public static final int CANDLE_ID = 1;

    // Outtake
    // TODO: change to proper values
    public static final int OUTTAKE_MOTOR = 0;
}
